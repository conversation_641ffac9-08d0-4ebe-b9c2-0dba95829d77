import 'package:equatable/equatable.dart';

/// {@template face_detection_result}
/// Represents the result of face detection analysis on a single frame.
/// {@endtemplate}
class FaceDetectionResult extends Equatable {
  /// {@macro face_detection_result}
  const FaceDetectionResult({
    required this.faceDetected,
    required this.faceCount,
    required this.coveragePercentage,
    required this.timestamp,
    this.boundingBox,
    this.confidence,
  });

  /// Whether a face was detected in the frame
  final bool faceDetected;

  /// Number of faces detected in the frame
  final int faceCount;

  /// Percentage of the face guide area covered by the detected face (0-100)
  final double coveragePercentage;

  /// Timestamp when this detection was performed
  final DateTime timestamp;

  /// Bounding box of the detected face (optional)
  final FaceBoundingBox? boundingBox;

  /// Confidence score of the face detection (0.0-1.0)
  final double? confidence;

  /// Whether the face coverage meets the minimum threshold (85%)
  /// for deep analysis
  bool get meetsThreshold => coveragePercentage >= 85.0;

  /// Whether this is a valid detection (face detected with good coverage)
  bool get isValidDetection => faceDetected && faceCount == 1 && meetsThreshold;

  @override
  List<Object?> get props => [
        faceDetected,
        faceCount,
        coveragePercentage,
        timestamp,
        boundingBox,
        confidence,
      ];

  @override
  String toString() {
    return 'FaceDetectionResult('
        'faceDetected: $faceDetected, '
        'faceCount: $faceCount, '
        'coveragePercentage: ${coveragePercentage.toStringAsFixed(1)}%, '
        'meetsThreshold: $meetsThreshold, '
        'timestamp: $timestamp'
        ')';
  }
}

/// {@template face_bounding_box}
/// Represents the bounding box coordinates of a detected face.
/// {@endtemplate}
class FaceBoundingBox extends Equatable {
  /// {@macro face_bounding_box}
  const FaceBoundingBox({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
  });

  /// Left coordinate of the bounding box
  final double left;

  /// Top coordinate of the bounding box
  final double top;

  /// Width of the bounding box
  final double width;

  /// Height of the bounding box
  final double height;

  /// Right coordinate of the bounding box
  double get right => left + width;

  /// Bottom coordinate of the bounding box
  double get bottom => top + height;

  /// Center X coordinate of the bounding box
  double get centerX => left + (width / 2);

  /// Center Y coordinate of the bounding box
  double get centerY => top + (height / 2);

  @override
  List<Object> get props => [left, top, width, height];

  @override
  String toString() {
    return 'FaceBoundingBox('
        'left: $left, '
        'top: $top, '
        'width: $width, '
        'height: $height'
        ')';
  }
}
